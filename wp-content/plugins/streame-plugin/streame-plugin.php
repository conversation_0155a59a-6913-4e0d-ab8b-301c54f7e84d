<?php
/**
 * Plugin Name: Streame - Movie & TV Show Streaming Plugin
 * Plugin URI: https://github.com/your-username/streame-plugin
 * Description: A comprehensive WordPress plugin for streaming movies and TV shows with TMDB API integration and multiple embed sources support.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: streame-plugin
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('STREAME_PLUGIN_VERSION', '1.0.0');
define('STREAME_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STREAME_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STREAME_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Streame Plugin Class
 */
class StreamePlugin {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('StreamePlugin', 'uninstall'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Core classes
        require_once STREAME_PLUGIN_PATH . 'includes/class-streame-post-types.php';
        require_once STREAME_PLUGIN_PATH . 'includes/class-streame-tmdb-api.php';
        require_once STREAME_PLUGIN_PATH . 'includes/class-streame-embed-sources.php';
        require_once STREAME_PLUGIN_PATH . 'includes/class-streame-admin.php';
        require_once STREAME_PLUGIN_PATH . 'includes/class-streame-frontend.php';
        require_once STREAME_PLUGIN_PATH . 'includes/class-streame-ajax.php';
        
        // Initialize classes
        new Streame_Post_Types();
        new Streame_TMDB_API();
        new Streame_Embed_Sources();
        new Streame_Admin();
        new Streame_Frontend();
        new Streame_Ajax();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize custom post types
        do_action('streame_init');
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('streame-plugin', false, dirname(STREAME_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set default options
        $this->set_default_options();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove plugin options
        delete_option('streame_tmdb_api_key');
        delete_option('streame_embed_sources');
        delete_option('streame_plugin_settings');
        
        // Remove custom post types and meta
        $posts = get_posts(array(
            'post_type' => array('streame_movie', 'streame_episode'),
            'numberposts' => -1,
            'post_status' => 'any'
        ));
        
        foreach ($posts as $post) {
            wp_delete_post($post->ID, true);
        }
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Table for storing TMDB cache
        $table_name = $wpdb->prefix . 'streame_tmdb_cache';
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            tmdb_id int(11) NOT NULL,
            media_type varchar(20) NOT NULL,
            data longtext NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY tmdb_media (tmdb_id, media_type)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        // Default embed sources
        $default_sources = array(
            array(
                'name' => 'MultiEmbed',
                'movie_url' => 'https://multiembed.mov/?video_id={TMDB_ID}&tmdb=1',
                'tv_url' => 'https://multiembed.mov/?video_id={TMDB_ID}&tmdb=1&s={SEASON}&e={EPISODE}',
                'enabled' => true
            )
        );
        
        add_option('streame_embed_sources', $default_sources);
        add_option('streame_plugin_settings', array(
            'cache_duration' => 24, // hours
            'recommendations_count' => 6,
            'auto_fetch_metadata' => true
        ));
    }
}

// Initialize the plugin
StreamePlugin::get_instance();
