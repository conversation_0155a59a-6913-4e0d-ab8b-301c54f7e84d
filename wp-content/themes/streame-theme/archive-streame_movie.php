<?php
/**
 * Template for displaying movie archive
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container">
        
        <!-- Archive Header -->
        <section class="archive-header">
            <div class="archive-header-content">
                <h1 class="archive-title"><?php _e('Movies', 'streame-theme'); ?></h1>
                <p class="archive-description"><?php _e('Discover amazing movies from all genres and eras', 'streame-theme'); ?></p>
            </div>
            
            <!-- Filters and Search -->
            <div class="archive-filters">
                <div class="filter-row">
                    <!-- Genre Filter -->
                    <div class="filter-group">
                        <label for="genre-filter"><?php _e('Genre:', 'streame-theme'); ?></label>
                        <select id="genre-filter" name="genre">
                            <option value=""><?php _e('All Genres', 'streame-theme'); ?></option>
                            <?php
                            $genres = get_terms(array(
                                'taxonomy' => 'streame_genre',
                                'hide_empty' => true,
                                'orderby' => 'name'
                            ));
                            
                            if ($genres && !is_wp_error($genres)):
                                foreach ($genres as $genre):
                                    $selected = (isset($_GET['genre']) && $_GET['genre'] == $genre->slug) ? 'selected' : '';
                            ?>
                                <option value="<?php echo esc_attr($genre->slug); ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($genre->name); ?> (<?php echo $genre->count; ?>)
                                </option>
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>
                    
                    <!-- Year Filter -->
                    <div class="filter-group">
                        <label for="year-filter"><?php _e('Year:', 'streame-theme'); ?></label>
                        <select id="year-filter" name="year">
                            <option value=""><?php _e('All Years', 'streame-theme'); ?></option>
                            <?php
                            $years = get_terms(array(
                                'taxonomy' => 'streame_year',
                                'hide_empty' => true,
                                'orderby' => 'name',
                                'order' => 'DESC'
                            ));
                            
                            if ($years && !is_wp_error($years)):
                                foreach ($years as $year):
                                    $selected = (isset($_GET['year']) && $_GET['year'] == $year->slug) ? 'selected' : '';
                            ?>
                                <option value="<?php echo esc_attr($year->slug); ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($year->name); ?> (<?php echo $year->count; ?>)
                                </option>
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>
                    
                    <!-- Sort Filter -->
                    <div class="filter-group">
                        <label for="sort-filter"><?php _e('Sort by:', 'streame-theme'); ?></label>
                        <select id="sort-filter" name="sort">
                            <option value="date" <?php selected(isset($_GET['sort']) ? $_GET['sort'] : '', 'date'); ?>>
                                <?php _e('Latest', 'streame-theme'); ?>
                            </option>
                            <option value="title" <?php selected(isset($_GET['sort']) ? $_GET['sort'] : '', 'title'); ?>>
                                <?php _e('Title A-Z', 'streame-theme'); ?>
                            </option>
                            <option value="rating" <?php selected(isset($_GET['sort']) ? $_GET['sort'] : '', 'rating'); ?>>
                                <?php _e('Highest Rated', 'streame-theme'); ?>
                            </option>
                            <option value="year" <?php selected(isset($_GET['sort']) ? $_GET['sort'] : '', 'year'); ?>>
                                <?php _e('Release Year', 'streame-theme'); ?>
                            </option>
                        </select>
                    </div>
                    
                    <!-- View Toggle -->
                    <div class="filter-group view-toggle">
                        <button class="view-btn grid-view active" data-view="grid" title="<?php _e('Grid View', 'streame-theme'); ?>">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 3H11V11H3V3ZM13 3H21V11H13V3ZM3 13H11V21H3V13ZM13 13H21V21H13V13Z"/>
                            </svg>
                        </button>
                        <button class="view-btn list-view" data-view="list" title="<?php _e('List View', 'streame-theme'); ?>">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 4H21V6H3V4ZM3 11H21V13H3V11ZM3 18H21V20H3V18Z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Search -->
                <div class="archive-search">
                    <?php echo streame_search_form(); ?>
                </div>
            </div>
        </section>
        
        <!-- Movies Grid -->
        <section class="movies-section">
            <?php if (have_posts()): ?>
                <div class="movies-grid content-grid" id="movies-container">
                    <?php while (have_posts()): the_post(); ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class('content-item movie-item'); ?>>
                            <div class="content-item-image">
                                <?php
                                $poster_url = get_post_meta(get_the_ID(), '_streame_poster_url', true);
                                if ($poster_url):
                                ?>
                                    <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" />
                                <?php else: ?>
                                    <div class="no-poster">
                                        <span class="movie-title"><?php the_title(); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="content-item-overlay">
                                    <?php
                                    $play_url = streame_get_play_url();
                                    if ($play_url):
                                    ?>
                                        <a href="<?php echo esc_url($play_url); ?>" class="play-button">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8 5V19L19 12L8 5Z"/>
                                            </svg>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="content-item-info">
                                <h3 class="content-item-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>
                                
                                <div class="content-item-meta">
                                    <?php
                                    $rating = streame_get_rating();
                                    if ($rating):
                                    ?>
                                        <span class="content-item-rating"><?php echo $rating; ?></span>
                                    <?php endif; ?>
                                    
                                    <?php
                                    $year = streame_get_year();
                                    if ($year):
                                    ?>
                                        <span class="content-item-year"><?php echo $year; ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="content-item-genres">
                                    <?php streame_display_genres(get_the_ID(), ', '); ?>
                                </div>
                                
                                <div class="content-item-description">
                                    <?php echo wp_trim_words(get_the_content(), 15); ?>
                                </div>
                                
                                <div class="content-item-actions">
                                    <a href="<?php the_permalink(); ?>" class="btn btn-secondary btn-small">
                                        <?php _e('More Info', 'streame-theme'); ?>
                                    </a>
                                    
                                    <?php if ($play_url): ?>
                                        <a href="<?php echo esc_url($play_url); ?>" class="btn btn-primary btn-small">
                                            <?php _e('Watch', 'streame-theme'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
                
                <!-- Load More Button -->
                <div class="load-more-section">
                    <button id="load-more-movies" class="btn btn-secondary" data-page="1" data-max-pages="<?php echo $wp_query->max_num_pages; ?>">
                        <?php _e('Load More Movies', 'streame-theme'); ?>
                    </button>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-section">
                    <?php streame_pagination(); ?>
                </div>
                
            <?php else: ?>
                <div class="no-movies">
                    <div class="no-content-message">
                        <h2><?php _e('No Movies Found', 'streame-theme'); ?></h2>
                        <p><?php _e('Sorry, no movies match your criteria. Try adjusting your filters or search terms.', 'streame-theme'); ?></p>
                        
                        <div class="no-content-actions">
                            <a href="<?php echo esc_url(get_post_type_archive_link('streame_movie')); ?>" class="btn btn-primary">
                                <?php _e('View All Movies', 'streame-theme'); ?>
                            </a>
                            <a href="<?php echo esc_url(get_post_type_archive_link('streame_tv_show')); ?>" class="btn btn-secondary">
                                <?php _e('Browse TV Shows', 'streame-theme'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </section>
        
        <!-- Popular Genres -->
        <section class="popular-genres">
            <h2><?php _e('Popular Genres', 'streame-theme'); ?></h2>
            
            <div class="genres-grid">
                <?php
                $popular_genres = get_terms(array(
                    'taxonomy' => 'streame_genre',
                    'number' => 8,
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'hide_empty' => true
                ));
                
                if ($popular_genres && !is_wp_error($popular_genres)):
                    foreach ($popular_genres as $genre):
                        // Get a random movie from this genre for background
                        $genre_movies = get_posts(array(
                            'post_type' => 'streame_movie',
                            'numberposts' => 1,
                            'orderby' => 'rand',
                            'tax_query' => array(
                                array(
                                    'taxonomy' => 'streame_genre',
                                    'field' => 'term_id',
                                    'terms' => $genre->term_id
                                )
                            )
                        ));
                        
                        $backdrop_url = '';
                        if (!empty($genre_movies)) {
                            $backdrop_url = get_post_meta($genre_movies[0]->ID, '_streame_backdrop_url', true);
                        }
                ?>
                    <div class="genre-item" <?php if ($backdrop_url): ?>style="background-image: linear-gradient(rgba(15, 23, 42, 0.8), rgba(15, 23, 42, 0.8)), url('<?php echo esc_url($backdrop_url); ?>');"<?php endif; ?>>
                        <a href="<?php echo get_term_link($genre); ?>" class="genre-link">
                            <h3 class="genre-name"><?php echo esc_html($genre->name); ?></h3>
                            <span class="genre-count">
                                <?php printf(_n('%d Movie', '%d Movies', $genre->count, 'streame-theme'), $genre->count); ?>
                            </span>
                        </a>
                    </div>
                <?php
                    endforeach;
                endif;
                ?>
            </div>
        </section>
        
    </div>
</main>

<script>
jQuery(document).ready(function($) {
    // Filter functionality
    $('#genre-filter, #year-filter, #sort-filter').on('change', function() {
        var genre = $('#genre-filter').val();
        var year = $('#year-filter').val();
        var sort = $('#sort-filter').val();
        
        var url = new URL(window.location);
        
        if (genre) {
            url.searchParams.set('genre', genre);
        } else {
            url.searchParams.delete('genre');
        }
        
        if (year) {
            url.searchParams.set('year', year);
        } else {
            url.searchParams.delete('year');
        }
        
        if (sort) {
            url.searchParams.set('sort', sort);
        } else {
            url.searchParams.delete('sort');
        }
        
        window.location.href = url.toString();
    });
    
    // View toggle
    $('.view-btn').on('click', function() {
        var view = $(this).data('view');
        
        $('.view-btn').removeClass('active');
        $(this).addClass('active');
        
        var $container = $('#movies-container');
        
        if (view === 'list') {
            $container.removeClass('content-grid').addClass('content-list');
        } else {
            $container.removeClass('content-list').addClass('content-grid');
        }
        
        // Save preference
        localStorage.setItem('movies_view', view);
    });
    
    // Load saved view preference
    var savedView = localStorage.getItem('movies_view');
    if (savedView) {
        $('.view-btn[data-view="' + savedView + '"]').click();
    }
    
    // Load more functionality
    $('#load-more-movies').on('click', function() {
        var $button = $(this);
        var page = parseInt($button.data('page')) + 1;
        var maxPages = parseInt($button.data('max-pages'));
        
        if (page > maxPages) {
            $button.hide();
            return;
        }
        
        $button.prop('disabled', true).text('<?php _e('Loading...', 'streame-theme'); ?>');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'load_more_movies',
                page: page,
                genre: '<?php echo isset($_GET['genre']) ? esc_js($_GET['genre']) : ''; ?>',
                year: '<?php echo isset($_GET['year']) ? esc_js($_GET['year']) : ''; ?>',
                sort: '<?php echo isset($_GET['sort']) ? esc_js($_GET['sort']) : ''; ?>',
                nonce: '<?php echo wp_create_nonce('load_more_movies'); ?>'
            },
            success: function(response) {
                if (response.success && response.data.html) {
                    $('#movies-container').append(response.data.html);
                    $button.data('page', page);
                    
                    if (page >= maxPages) {
                        $button.hide();
                    }
                } else {
                    $button.hide();
                }
            },
            error: function() {
                alert('<?php _e('Error loading more movies', 'streame-theme'); ?>');
            },
            complete: function() {
                $button.prop('disabled', false).text('<?php _e('Load More Movies', 'streame-theme'); ?>');
            }
        });
    });
});
</script>

<?php get_footer(); ?>
