<?php
/**
 * Template for displaying single TV show
 */

get_header(); ?>

<main id="primary" class="site-main">
    <?php while (have_posts()): the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class('tv-show-single'); ?>>
            
            <?php
            // Get TV show metadata
            $tmdb_id = get_post_meta(get_the_ID(), '_streame_tmdb_id', true);
            $poster_url = get_post_meta(get_the_ID(), '_streame_poster_url', true);
            $backdrop_url = get_post_meta(get_the_ID(), '_streame_backdrop_url', true);
            $rating = streame_get_rating();
            $year = streame_get_year();
            $first_air_date = get_post_meta(get_the_ID(), '_streame_first_air_date', true);
            $last_air_date = get_post_meta(get_the_ID(), '_streame_last_air_date', true);
            $seasons = get_post_meta(get_the_ID(), '_streame_seasons', true);
            $episodes = get_post_meta(get_the_ID(), '_streame_episodes', true);
            $genres = streame_get_genres();
            ?>
            
            <!-- Hero Section with Backdrop -->
            <section class="tv-show-hero" <?php if ($backdrop_url): ?>style="background-image: linear-gradient(rgba(15, 23, 42, 0.7), rgba(15, 23, 42, 0.9)), url('<?php echo esc_url($backdrop_url); ?>');"<?php endif; ?>>
                <div class="container">
                    <div class="tv-show-hero-content">
                        <?php if ($poster_url): ?>
                            <div class="tv-show-poster">
                                <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" />
                            </div>
                        <?php endif; ?>
                        
                        <div class="tv-show-info">
                            <h1 class="tv-show-title"><?php the_title(); ?></h1>
                            
                            <div class="tv-show-meta">
                                <?php if ($rating): ?>
                                    <span class="tv-show-rating">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                                        </svg>
                                        <?php echo $rating; ?>/10
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($year): ?>
                                    <span class="tv-show-year"><?php echo $year; ?></span>
                                <?php endif; ?>
                                
                                <?php if ($seasons): ?>
                                    <span class="tv-show-seasons">
                                        <?php printf(_n('%d Season', '%d Seasons', $seasons, 'streame-theme'), $seasons); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($episodes): ?>
                                    <span class="tv-show-episodes">
                                        <?php printf(_n('%d Episode', '%d Episodes', $episodes, 'streame-theme'), $episodes); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (!empty($genres)): ?>
                                    <span class="tv-show-genres">
                                        <?php streame_display_genres(); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="tv-show-description">
                                <?php the_content(); ?>
                            </div>
                            
                            <div class="tv-show-actions">
                                <button class="btn btn-primary watch-trailer" data-tmdb-id="<?php echo $tmdb_id; ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5V19L19 12L8 5Z"/>
                                    </svg>
                                    <?php _e('Watch Trailer', 'streame-theme'); ?>
                                </button>
                                
                                <button class="btn btn-secondary add-to-watchlist" data-show-id="<?php echo get_the_ID(); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M19 14C19 18.4183 12 23 12 23C12 23 5 18.4183 5 14C5 11.2386 7.23858 9 10 9C10.7403 9 11.4215 9.19716 12 9.53799C12.5785 9.19716 13.2597 9 14 9C16.7614 9 19 11.2386 19 14Z"/>
                                    </svg>
                                    <?php _e('Add to Watchlist', 'streame-theme'); ?>
                                </button>
                                
                                <button class="btn btn-secondary share-show" data-url="<?php echo get_permalink(); ?>" data-title="<?php echo esc_attr(get_the_title()); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M4 12V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V12"/>
                                        <path d="M16 6L12 2L8 6"/>
                                        <path d="M12 2V15"/>
                                    </svg>
                                    <?php _e('Share', 'streame-theme'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Episodes Section -->
            <section class="tv-show-episodes-section">
                <div class="container">
                    <h2><?php _e('Episodes', 'streame-theme'); ?></h2>
                    
                    <?php
                    // Get episodes for this TV show
                    $show_episodes = get_posts(array(
                        'post_type' => 'streame_episode',
                        'numberposts' => -1,
                        'meta_key' => '_streame_tv_show_id',
                        'meta_value' => get_the_ID(),
                        'meta_key' => '_streame_season_number',
                        'orderby' => 'meta_value_num',
                        'order' => 'ASC'
                    ));
                    
                    if ($show_episodes):
                        // Group episodes by season
                        $episodes_by_season = array();
                        foreach ($show_episodes as $episode) {
                            $season_num = get_post_meta($episode->ID, '_streame_season_number', true);
                            if (!isset($episodes_by_season[$season_num])) {
                                $episodes_by_season[$season_num] = array();
                            }
                            $episodes_by_season[$season_num][] = $episode;
                        }
                        
                        ksort($episodes_by_season);
                    ?>
                        <div class="seasons-container">
                            <?php foreach ($episodes_by_season as $season_num => $season_episodes): ?>
                                <div class="season-section">
                                    <h3 class="season-title">
                                        <?php printf(__('Season %d', 'streame-theme'), $season_num); ?>
                                        <span class="episode-count">
                                            <?php printf(_n('%d Episode', '%d Episodes', count($season_episodes), 'streame-theme'), count($season_episodes)); ?>
                                        </span>
                                    </h3>
                                    
                                    <div class="episodes-grid">
                                        <?php foreach ($season_episodes as $episode): ?>
                                            <?php
                                            $episode_num = get_post_meta($episode->ID, '_streame_episode_number', true);
                                            $episode_rating = streame_get_rating($episode->ID);
                                            $air_date = get_post_meta($episode->ID, '_streame_air_date', true);
                                            $still_path = get_post_meta($episode->ID, '_streame_still_path', true);
                                            $play_url = streame_get_play_url($episode->ID);
                                            ?>
                                            
                                            <div class="episode-item">
                                                <div class="episode-thumbnail">
                                                    <?php if ($still_path): ?>
                                                        <img src="<?php echo esc_url($still_path); ?>" alt="<?php echo esc_attr($episode->post_title); ?>" />
                                                    <?php else: ?>
                                                        <div class="no-thumbnail">
                                                            <span class="episode-number"><?php echo $episode_num; ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($play_url): ?>
                                                        <div class="episode-overlay">
                                                            <a href="<?php echo esc_url($play_url); ?>" class="play-episode">
                                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                                                    <path d="M8 5V19L19 12L8 5Z"/>
                                                                </svg>
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="episode-info">
                                                    <div class="episode-header">
                                                        <h4 class="episode-title">
                                                            <?php printf(__('%d. %s', 'streame-theme'), $episode_num, $episode->post_title); ?>
                                                        </h4>
                                                        
                                                        <div class="episode-meta">
                                                            <?php if ($episode_rating): ?>
                                                                <span class="episode-rating"><?php echo $episode_rating; ?></span>
                                                            <?php endif; ?>
                                                            
                                                            <?php if ($air_date): ?>
                                                                <span class="episode-date"><?php echo date('M j, Y', strtotime($air_date)); ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="episode-description">
                                                        <?php echo wp_trim_words($episode->post_content, 20); ?>
                                                    </div>
                                                    
                                                    <?php if ($play_url): ?>
                                                        <div class="episode-actions">
                                                            <a href="<?php echo esc_url($play_url); ?>" class="btn btn-primary btn-small">
                                                                <?php _e('Watch', 'streame-theme'); ?>
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-episodes">
                            <p><?php _e('No episodes available yet.', 'streame-theme'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </section>
            
            <!-- TV Show Details -->
            <section class="tv-show-details">
                <div class="container">
                    <div class="details-grid">
                        <div class="details-main">
                            
                            <!-- Additional Info -->
                            <div class="tv-show-additional-info">
                                <h2><?php _e('Details', 'streame-theme'); ?></h2>
                                
                                <div class="info-grid">
                                    <?php if ($first_air_date): ?>
                                        <div class="info-item">
                                            <strong><?php _e('First Air Date:', 'streame-theme'); ?></strong>
                                            <span><?php echo date('F j, Y', strtotime($first_air_date)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($last_air_date): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Last Air Date:', 'streame-theme'); ?></strong>
                                            <span><?php echo date('F j, Y', strtotime($last_air_date)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($seasons): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Seasons:', 'streame-theme'); ?></strong>
                                            <span><?php echo $seasons; ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($episodes): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Total Episodes:', 'streame-theme'); ?></strong>
                                            <span><?php echo $episodes; ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($genres)): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Genres:', 'streame-theme'); ?></strong>
                                            <span><?php streame_display_genres(); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($tmdb_id): ?>
                                        <div class="info-item">
                                            <strong><?php _e('TMDB ID:', 'streame-theme'); ?></strong>
                                            <span><?php echo $tmdb_id; ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Cast & Crew (if available from TMDB) -->
                            <?php if (class_exists('Streame_TMDB_API') && $tmdb_id): ?>
                                <div class="tv-show-cast-crew">
                                    <h2><?php _e('Cast & Crew', 'streame-theme'); ?></h2>
                                    <div id="cast-crew-content" data-tmdb-id="<?php echo $tmdb_id; ?>" data-media-type="tv">
                                        <div class="loading"><?php _e('Loading cast information...', 'streame-theme'); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                        </div>
                        
                        <div class="details-sidebar">
                            <!-- TV Show Poster -->
                            <?php if ($poster_url): ?>
                                <div class="sidebar-poster">
                                    <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" />
                                </div>
                            <?php endif; ?>
                            
                            <!-- Quick Stats -->
                            <div class="quick-stats">
                                <h3><?php _e('Quick Stats', 'streame-theme'); ?></h3>
                                
                                <?php if ($rating): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('Rating', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php echo $rating; ?>/10</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($year): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('First Aired', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php echo $year; ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($seasons): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('Seasons', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php echo $seasons; ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($episodes): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('Episodes', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php echo $episodes; ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Related TV Shows by Genre -->
                            <?php if (!empty($genres)): ?>
                                <div class="related-shows">
                                    <h3><?php _e('More Like This', 'streame-theme'); ?></h3>
                                    
                                    <?php
                                    $related_shows = get_posts(array(
                                        'post_type' => 'streame_tv_show',
                                        'numberposts' => 4,
                                        'post__not_in' => array(get_the_ID()),
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => 'streame_genre',
                                                'field' => 'term_id',
                                                'terms' => wp_list_pluck($genres, 'term_id')
                                            )
                                        )
                                    ));
                                    
                                    if ($related_shows):
                                    ?>
                                        <div class="related-shows-list">
                                            <?php foreach ($related_shows as $related_show): ?>
                                                <div class="related-show-item">
                                                    <?php
                                                    $related_poster = get_post_meta($related_show->ID, '_streame_poster_url', true);
                                                    if ($related_poster):
                                                    ?>
                                                        <div class="related-show-poster">
                                                            <a href="<?php echo get_permalink($related_show->ID); ?>">
                                                                <img src="<?php echo esc_url($related_poster); ?>" alt="<?php echo esc_attr($related_show->post_title); ?>" />
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="related-show-info">
                                                        <h4>
                                                            <a href="<?php echo get_permalink($related_show->ID); ?>">
                                                                <?php echo esc_html($related_show->post_title); ?>
                                                            </a>
                                                        </h4>
                                                        
                                                        <?php
                                                        $related_rating = streame_get_rating($related_show->ID);
                                                        $related_year = streame_get_year($related_show->ID);
                                                        ?>
                                                        
                                                        <div class="related-show-meta">
                                                            <?php if ($related_rating): ?>
                                                                <span class="rating"><?php echo $related_rating; ?></span>
                                                            <?php endif; ?>
                                                            
                                                            <?php if ($related_year): ?>
                                                                <span class="year"><?php echo $related_year; ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Recommendations Section -->
            <?php if (class_exists('Streame_TMDB_API') && $tmdb_id): ?>
                <section class="tv-show-recommendations">
                    <div class="container">
                        <h2><?php _e('Maybe You\'ll Like', 'streame-theme'); ?></h2>
                        <div id="recommendations-content" data-tmdb-id="<?php echo $tmdb_id; ?>" data-media-type="tv">
                            <div class="loading"><?php _e('Loading recommendations...', 'streame-theme'); ?></div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>
            
            <!-- Navigation -->
            <section class="tv-show-navigation">
                <div class="container">
                    <?php streame_theme_post_navigation(); ?>
                </div>
            </section>
            
        </article>
    <?php endwhile; ?>
</main>

<?php get_footer(); ?>
