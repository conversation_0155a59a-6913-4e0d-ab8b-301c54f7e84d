<?php
/**
 * The main template file
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php if (is_home() && !is_paged()): ?>
            <!-- Hero Section -->
            <section class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title"><?php bloginfo('name'); ?></h1>
                    <p class="hero-subtitle"><?php bloginfo('description'); ?></p>
                    <div class="hero-actions">
                        <a href="<?php echo esc_url(get_post_type_archive_link('streame_movie')); ?>" class="btn btn-primary btn-large">
                            <?php _e('Browse Movies', 'streame-theme'); ?>
                        </a>
                        <a href="<?php echo esc_url(get_post_type_archive_link('streame_tv_show')); ?>" class="btn btn-secondary btn-large">
                            <?php _e('Browse TV Shows', 'streame-theme'); ?>
                        </a>
                    </div>
                </div>
            </section>
            
            <?php if (streame_plugin_active()): ?>
                <!-- Featured Content -->
                <?php
                $featured_movies = get_posts(array(
                    'post_type' => 'streame_movie',
                    'numberposts' => 1,
                    'meta_key' => '_streame_rating',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC'
                ));
                
                if ($featured_movies):
                    $featured_movie = $featured_movies[0];
                    $poster_url = get_post_meta($featured_movie->ID, '_streame_poster_url', true);
                    $backdrop_url = get_post_meta($featured_movie->ID, '_streame_backdrop_url', true);
                    $rating = streame_get_rating($featured_movie->ID);
                    $year = streame_get_year($featured_movie->ID);
                    $genres = streame_get_genres($featured_movie->ID);
                ?>
                    <section class="featured-content" <?php if ($backdrop_url): ?>style="background-image: linear-gradient(rgba(15, 23, 42, 0.8), rgba(15, 23, 42, 0.8)), url('<?php echo esc_url($backdrop_url); ?>');"<?php endif; ?>>
                        <div class="featured-content-inner">
                            <?php if ($poster_url): ?>
                                <div class="featured-poster">
                                    <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr($featured_movie->post_title); ?>" />
                                </div>
                            <?php endif; ?>
                            
                            <div class="featured-info">
                                <h2><?php echo esc_html($featured_movie->post_title); ?></h2>
                                
                                <div class="featured-meta">
                                    <?php if ($rating): ?>
                                        <span class="featured-rating"><?php echo $rating; ?>/10</span>
                                    <?php endif; ?>
                                    
                                    <?php if ($year): ?>
                                        <span class="featured-year"><?php echo $year; ?></span>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($genres)): ?>
                                        <span class="featured-genre">
                                            <?php echo esc_html($genres[0]->name); ?>
                                            <?php if (count($genres) > 1): ?>
                                                <?php printf(__(' +%d more', 'streame-theme'), count($genres) - 1); ?>
                                            <?php endif; ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="featured-description">
                                    <?php echo wp_trim_words($featured_movie->post_content, 30); ?>
                                </div>
                                
                                <div class="featured-actions">
                                    <?php streame_display_play_button($featured_movie->ID, __('Watch Now', 'streame-theme')); ?>
                                    <a href="<?php echo get_permalink($featured_movie->ID); ?>" class="btn btn-secondary">
                                        <?php _e('More Info', 'streame-theme'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                <?php endif; ?>
                
                <!-- Popular Movies -->
                <?php
                $popular_movies = get_posts(array(
                    'post_type' => 'streame_movie',
                    'numberposts' => 8,
                    'meta_key' => '_streame_rating',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC'
                ));
                
                if ($popular_movies): ?>
                    <section class="content-section">
                        <div class="section-header">
                            <h2 class="section-title"><?php _e('Popular Movies', 'streame-theme'); ?></h2>
                            <p class="section-subtitle"><?php _e('Trending movies everyone is watching', 'streame-theme'); ?></p>
                        </div>
                        
                        <div class="content-grid">
                            <?php foreach ($popular_movies as $movie): ?>
                                <article class="content-item">
                                    <div class="content-item-image">
                                        <?php
                                        $poster_url = get_post_meta($movie->ID, '_streame_poster_url', true);
                                        if ($poster_url):
                                        ?>
                                            <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr($movie->post_title); ?>" />
                                        <?php else: ?>
                                            <div class="no-poster"><?php echo esc_html($movie->post_title); ?></div>
                                        <?php endif; ?>
                                        
                                        <div class="content-item-overlay">
                                            <?php
                                            $play_url = streame_get_play_url($movie->ID);
                                            if ($play_url):
                                            ?>
                                                <a href="<?php echo esc_url($play_url); ?>" class="play-button">
                                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M8 5v14l11-7z"/>
                                                    </svg>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="content-item-info">
                                        <h3 class="content-item-title">
                                            <a href="<?php echo get_permalink($movie->ID); ?>">
                                                <?php echo esc_html($movie->post_title); ?>
                                            </a>
                                        </h3>
                                        
                                        <div class="content-item-meta">
                                            <?php
                                            $rating = streame_get_rating($movie->ID);
                                            if ($rating):
                                            ?>
                                                <span class="content-item-rating"><?php echo $rating; ?></span>
                                            <?php endif; ?>
                                            
                                            <?php
                                            $year = streame_get_year($movie->ID);
                                            if ($year):
                                            ?>
                                                <span class="content-item-year"><?php echo $year; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="content-item-description">
                                            <?php echo wp_trim_words($movie->post_content, 15); ?>
                                        </div>
                                    </div>
                                </article>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="section-footer">
                            <a href="<?php echo esc_url(get_post_type_archive_link('streame_movie')); ?>" class="btn btn-secondary">
                                <?php _e('View All Movies', 'streame-theme'); ?>
                            </a>
                        </div>
                    </section>
                <?php endif; ?>
                
                <!-- Popular TV Shows -->
                <?php
                $popular_tv_shows = get_posts(array(
                    'post_type' => 'streame_tv_show',
                    'numberposts' => 8,
                    'meta_key' => '_streame_rating',
                    'orderby' => 'meta_value_num',
                    'order' => 'DESC'
                ));
                
                if ($popular_tv_shows): ?>
                    <section class="content-section">
                        <div class="section-header">
                            <h2 class="section-title"><?php _e('Popular TV Shows', 'streame-theme'); ?></h2>
                            <p class="section-subtitle"><?php _e('Binge-worthy series you can\'t miss', 'streame-theme'); ?></p>
                        </div>
                        
                        <div class="content-grid">
                            <?php foreach ($popular_tv_shows as $tv_show): ?>
                                <article class="content-item">
                                    <div class="content-item-image">
                                        <?php
                                        $poster_url = get_post_meta($tv_show->ID, '_streame_poster_url', true);
                                        if ($poster_url):
                                        ?>
                                            <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr($tv_show->post_title); ?>" />
                                        <?php else: ?>
                                            <div class="no-poster"><?php echo esc_html($tv_show->post_title); ?></div>
                                        <?php endif; ?>
                                        
                                        <div class="content-item-overlay">
                                            <a href="<?php echo get_permalink($tv_show->ID); ?>" class="play-button">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M8 5v14l11-7z"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <div class="content-item-info">
                                        <h3 class="content-item-title">
                                            <a href="<?php echo get_permalink($tv_show->ID); ?>">
                                                <?php echo esc_html($tv_show->post_title); ?>
                                            </a>
                                        </h3>
                                        
                                        <div class="content-item-meta">
                                            <?php
                                            $rating = streame_get_rating($tv_show->ID);
                                            if ($rating):
                                            ?>
                                                <span class="content-item-rating"><?php echo $rating; ?></span>
                                            <?php endif; ?>
                                            
                                            <?php
                                            $year = streame_get_year($tv_show->ID);
                                            if ($year):
                                            ?>
                                                <span class="content-item-year"><?php echo $year; ?></span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="content-item-description">
                                            <?php echo wp_trim_words($tv_show->post_content, 15); ?>
                                        </div>
                                    </div>
                                </article>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="section-footer">
                            <a href="<?php echo esc_url(get_post_type_archive_link('streame_tv_show')); ?>" class="btn btn-secondary">
                                <?php _e('View All TV Shows', 'streame-theme'); ?>
                            </a>
                        </div>
                    </section>
                <?php endif; ?>
                
            <?php else: ?>
                <!-- Fallback content when plugin is not active -->
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title"><?php _e('Latest Posts', 'streame-theme'); ?></h2>
                    </div>
                </section>
            <?php endif; ?>
            
        <?php endif; ?>
        
        <!-- Blog Posts -->
        <?php if (have_posts()): ?>
            <?php if (!is_home() || is_paged()): ?>
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <?php
                            if (is_search()) {
                                printf(__('Search Results for: %s', 'streame-theme'), get_search_query());
                            } elseif (is_archive()) {
                                the_archive_title();
                            } else {
                                _e('Latest Posts', 'streame-theme');
                            }
                            ?>
                        </h2>
                    </div>
                    
                    <div class="posts-grid">
                        <?php while (have_posts()): the_post(); ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('post-item'); ?>>
                                <?php if (has_post_thumbnail()): ?>
                                    <div class="post-thumbnail">
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium'); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="post-content">
                                    <h3 class="post-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    
                                    <div class="post-meta">
                                        <span class="post-date"><?php echo get_the_date(); ?></span>
                                        <span class="post-author"><?php _e('by', 'streame-theme'); ?> <?php the_author(); ?></span>
                                    </div>
                                    
                                    <div class="post-excerpt">
                                        <?php the_excerpt(); ?>
                                    </div>
                                    
                                    <a href="<?php the_permalink(); ?>" class="read-more">
                                        <?php _e('Read More', 'streame-theme'); ?>
                                    </a>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>
                    
                    <?php streame_pagination(); ?>
                </section>
            <?php endif; ?>
        <?php else: ?>
            <section class="content-section">
                <div class="no-content">
                    <h2><?php _e('Nothing Found', 'streame-theme'); ?></h2>
                    <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'streame-theme'); ?></p>
                    <?php echo streame_search_form(); ?>
                </div>
            </section>
        <?php endif; ?>
        
    </div>
</main>

<?php get_footer(); ?>
