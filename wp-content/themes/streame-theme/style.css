/*
Theme Name: Streame - Movie & TV Streaming Theme
Description: A modern, dark-themed WordPress theme designed specifically for movie and TV show streaming websites. Features responsive design, dark blue accents, and seamless integration with the Streame plugin.
Author: Your Name
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: streame-theme
Tags: streaming, movies, tv-shows, dark-theme, responsive, entertainment
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
*/

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #e2e8f0;
    background: #0f172a;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: #fff;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: 1rem;
    color: #cbd5e1;
}

a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #1e3a8a;
}

/* Layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
    flex: 1;
}

/* Header */
.site-header {
    background: #1e293b;
    border-bottom: 1px solid #334155;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.site-logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: #fff;
    text-decoration: none;
}

.site-logo:hover {
    color: #1e3a8a;
}

/* Navigation */
.main-navigation {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav-menu a {
    color: #cbd5e1;
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.current {
    color: #1e3a8a;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: #1e3a8a;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.current::after {
    width: 100%;
}

/* Search Form */
.header-search {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
    background: #334155;
    border-radius: 25px;
    padding: 0.5rem 1rem;
    min-width: 300px;
}

.search-form input[type="search"] {
    background: transparent;
    border: none;
    color: #fff;
    font-size: 1rem;
    width: 100%;
    outline: none;
}

.search-form input[type="search"]::placeholder {
    color: #94a3b8;
}

.search-form button {
    background: transparent;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
}

.search-form button:hover {
    color: #1e3a8a;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: transparent;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #fff 0%, #1e3a8a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #94a3b8;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: #1e3a8a;
    color: #fff;
}

.btn-primary:hover {
    background: #1e40af;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #1e3a8a;
    border: 2px solid #1e3a8a;
}

.btn-secondary:hover {
    background: #1e3a8a;
    color: #fff;
}

.btn-large {
    padding: 1rem 2.5rem;
    font-size: 1.125rem;
}

/* Content Sections */
.content-section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: #94a3b8;
    max-width: 600px;
    margin: 0 auto;
}

/* Movie/TV Show Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.content-item {
    background: #1e293b;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.content-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.content-item-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 2/3;
}

.content-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.content-item:hover .content-item-image img {
    transform: scale(1.05);
}

.content-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, transparent 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.content-item:hover .content-item-overlay {
    opacity: 1;
}

.play-button {
    background: #1e3a8a;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: #1e40af;
    transform: scale(1.1);
}

.content-item-info {
    padding: 1.5rem;
}

.content-item-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #fff;
}

.content-item-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.content-item-rating {
    background: #1e3a8a;
    color: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
}

.content-item-year {
    color: #94a3b8;
    font-size: 0.875rem;
}

.content-item-description {
    color: #cbd5e1;
    font-size: 0.875rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Featured Content */
.featured-content {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-radius: 16px;
    padding: 3rem;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.featured-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.featured-content-inner {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: center;
}

.featured-poster {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.featured-poster img {
    width: 100%;
    height: auto;
    display: block;
}

.featured-info h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.featured-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.featured-rating {
    background: #1e3a8a;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
}

.featured-year,
.featured-genre {
    color: #94a3b8;
}

.featured-description {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #cbd5e1;
}

.featured-actions {
    display: flex;
    gap: 1rem;
}

/* Footer */
.site-footer {
    background: #1e293b;
    border-top: 1px solid #334155;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #fff;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #94a3b8;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #1e3a8a;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 2rem;
    text-align: center;
    color: #94a3b8;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .featured-content-inner {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .search-form {
        min-width: 250px;
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-navigation {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #1e293b;
        border-top: 1px solid #334155;
        padding: 1rem;
    }
    
    .main-navigation.active {
        display: block;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-search {
        order: -1;
        margin-bottom: 1rem;
    }
    
    .search-form {
        min-width: 100%;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .featured-content {
        padding: 2rem;
    }
    
    .featured-content-inner {
        gap: 2rem;
    }
    
    .featured-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .content-item-info {
        padding: 1rem;
    }
    
    .featured-content {
        padding: 1.5rem;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    /* Already dark by default */
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    body {
        background: #000;
        color: #fff;
    }
    
    .site-header,
    .content-item,
    .featured-content,
    .site-footer {
        background: #111;
        border-color: #fff;
    }
    
    .btn-primary {
        background: #0066cc;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}
