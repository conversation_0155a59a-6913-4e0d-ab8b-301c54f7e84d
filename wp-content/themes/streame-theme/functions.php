<?php
/**
 * Streame Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme constants
define('STREAME_THEME_VERSION', '1.0.0');
define('STREAME_THEME_URL', get_template_directory_uri());
define('STREAME_THEME_PATH', get_template_directory());

/**
 * Theme setup
 */
function streame_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    add_theme_support('custom-logo', array(
        'height' => 50,
        'width' => 200,
        'flex-height' => true,
        'flex-width' => true
    ));
    add_theme_support('custom-background');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Add image sizes
    add_image_size('streame-poster', 300, 450, true);
    add_image_size('streame-backdrop', 1280, 720, true);
    add_image_size('streame-thumbnail', 200, 300, true);
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'streame-theme'),
        'footer' => __('Footer Menu', 'streame-theme')
    ));
    
    // Load text domain
    load_theme_textdomain('streame-theme', STREAME_THEME_PATH . '/languages');
}
add_action('after_setup_theme', 'streame_theme_setup');

/**
 * Enqueue scripts and styles
 */
function streame_theme_scripts() {
    // Styles
    wp_enqueue_style('streame-theme-style', get_stylesheet_uri(), array(), STREAME_THEME_VERSION);
    wp_enqueue_style('streame-theme-main', STREAME_THEME_URL . '/assets/css/main.css', array(), STREAME_THEME_VERSION);
    
    // Google Fonts
    wp_enqueue_style('streame-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap', array(), null);
    
    // Scripts
    wp_enqueue_script('streame-theme-main', STREAME_THEME_URL . '/assets/js/main.js', array('jquery'), STREAME_THEME_VERSION, true);
    
    // Localize script
    wp_localize_script('streame-theme-main', 'streame_theme', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('streame_theme_nonce'),
        'strings' => array(
            'loading' => __('Loading...', 'streame-theme'),
            'error' => __('Error loading content', 'streame-theme'),
            'no_results' => __('No results found', 'streame-theme'),
            'load_more' => __('Load More', 'streame-theme')
        )
    ));
    
    // Conditional scripts
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'streame_theme_scripts');

/**
 * Register widget areas
 */
function streame_theme_widgets_init() {
    register_sidebar(array(
        'name' => __('Sidebar', 'streame-theme'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here.', 'streame-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget' => '</section>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>'
    ));
    
    register_sidebar(array(
        'name' => __('Footer 1', 'streame-theme'),
        'id' => 'footer-1',
        'description' => __('Footer widget area 1.', 'streame-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>'
    ));
    
    register_sidebar(array(
        'name' => __('Footer 2', 'streame-theme'),
        'id' => 'footer-2',
        'description' => __('Footer widget area 2.', 'streame-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>'
    ));
    
    register_sidebar(array(
        'name' => __('Footer 3', 'streame-theme'),
        'id' => 'footer-3',
        'description' => __('Footer widget area 3.', 'streame-theme'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>'
    ));
}
add_action('widgets_init', 'streame_theme_widgets_init');

/**
 * Custom excerpt length
 */
function streame_theme_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'streame_theme_excerpt_length');

/**
 * Custom excerpt more
 */
function streame_theme_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'streame_theme_excerpt_more');

/**
 * Add body classes
 */
function streame_theme_body_classes($classes) {
    // Add class for Streame plugin compatibility
    if (class_exists('StreamePlugin')) {
        $classes[] = 'streame-plugin-active';
    }
    
    // Add class for dark theme
    $classes[] = 'streame-dark';
    
    // Add class for current post type
    if (is_singular()) {
        $classes[] = 'single-' . get_post_type();
    }
    
    return $classes;
}
add_filter('body_class', 'streame_theme_body_classes');

/**
 * Customize login page
 */
function streame_theme_login_styles() {
    ?>
    <style type="text/css">
        body.login {
            background: #0f172a;
        }
        
        .login h1 a {
            background-image: none;
            background-color: #1e3a8a;
            color: #fff;
            text-decoration: none;
            width: auto;
            height: auto;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .login form {
            background: #1e293b;
            border: 1px solid #334155;
        }
        
        .login form .input {
            background: #334155;
            border: 1px solid #475569;
            color: #fff;
        }
        
        .login form .button-primary {
            background: #1e3a8a;
            border-color: #1e3a8a;
        }
        
        .login form .button-primary:hover {
            background: #1e40af;
            border-color: #1e40af;
        }
        
        .login #nav a,
        .login #backtoblog a {
            color: #94a3b8;
        }
        
        .login #nav a:hover,
        .login #backtoblog a:hover {
            color: #1e3a8a;
        }
    </style>
    <?php
}
add_action('login_enqueue_scripts', 'streame_theme_login_styles');

/**
 * Change login logo URL
 */
function streame_theme_login_logo_url() {
    return home_url();
}
add_filter('login_headerurl', 'streame_theme_login_logo_url');

/**
 * Change login logo title
 */
function streame_theme_login_logo_url_title() {
    return get_bloginfo('name');
}
add_filter('login_headertitle', 'streame_theme_login_logo_url_title');

/**
 * Custom post navigation
 */
function streame_theme_post_navigation() {
    $prev_post = get_previous_post();
    $next_post = get_next_post();
    
    if ($prev_post || $next_post) {
        echo '<nav class="post-navigation">';
        
        if ($prev_post) {
            echo '<div class="nav-previous">';
            echo '<a href="' . get_permalink($prev_post) . '" rel="prev">';
            echo '<span class="nav-subtitle">' . __('Previous', 'streame-theme') . '</span>';
            echo '<span class="nav-title">' . get_the_title($prev_post) . '</span>';
            echo '</a>';
            echo '</div>';
        }
        
        if ($next_post) {
            echo '<div class="nav-next">';
            echo '<a href="' . get_permalink($next_post) . '" rel="next">';
            echo '<span class="nav-subtitle">' . __('Next', 'streame-theme') . '</span>';
            echo '<span class="nav-title">' . get_the_title($next_post) . '</span>';
            echo '</a>';
            echo '</div>';
        }
        
        echo '</nav>';
    }
}

/**
 * Get movie/TV show rating
 */
function streame_get_rating($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $rating = get_post_meta($post_id, '_streame_rating', true);
    
    if ($rating) {
        return number_format($rating, 1);
    }
    
    return '';
}

/**
 * Get movie/TV show year
 */
function streame_get_year($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $post_type = get_post_type($post_id);
    
    if ($post_type === 'streame_movie') {
        $date = get_post_meta($post_id, '_streame_release_date', true);
    } elseif ($post_type === 'streame_tv_show') {
        $date = get_post_meta($post_id, '_streame_first_air_date', true);
    } else {
        return '';
    }
    
    if ($date) {
        return date('Y', strtotime($date));
    }
    
    return '';
}

/**
 * Get play URL for content
 */
function streame_get_play_url($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $post_type = get_post_type($post_id);
    $tmdb_id = get_post_meta($post_id, '_streame_tmdb_id', true);
    
    if (!$tmdb_id) {
        return '';
    }
    
    if ($post_type === 'streame_movie') {
        return home_url("watch/movie/{$tmdb_id}/");
    } elseif ($post_type === 'streame_episode') {
        return home_url("watch/episode/{$post_id}/");
    }
    
    return '';
}

/**
 * Display content rating
 */
function streame_display_rating($post_id = null) {
    $rating = streame_get_rating($post_id);
    
    if ($rating) {
        echo '<span class="content-rating">' . $rating . '/10</span>';
    }
}

/**
 * Display content year
 */
function streame_display_year($post_id = null) {
    $year = streame_get_year($post_id);
    
    if ($year) {
        echo '<span class="content-year">' . $year . '</span>';
    }
}

/**
 * Display play button
 */
function streame_display_play_button($post_id = null, $text = null) {
    $play_url = streame_get_play_url($post_id);
    
    if ($play_url) {
        $button_text = $text ?: __('Play', 'streame-theme');
        echo '<a href="' . esc_url($play_url) . '" class="btn btn-primary play-btn">' . esc_html($button_text) . '</a>';
    }
}

/**
 * Get content genres
 */
function streame_get_genres($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $genres = get_the_terms($post_id, 'streame_genre');
    
    if ($genres && !is_wp_error($genres)) {
        return $genres;
    }
    
    return array();
}

/**
 * Display content genres
 */
function streame_display_genres($post_id = null, $separator = ', ') {
    $genres = streame_get_genres($post_id);
    
    if (!empty($genres)) {
        $genre_names = array();
        foreach ($genres as $genre) {
            $genre_names[] = '<a href="' . get_term_link($genre) . '">' . $genre->name . '</a>';
        }
        echo implode($separator, $genre_names);
    }
}

/**
 * Pagination
 */
function streame_pagination() {
    global $wp_query;
    
    $big = 999999999;
    
    $paginate_links = paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'prev_text' => __('&laquo; Previous', 'streame-theme'),
        'next_text' => __('Next &raquo;', 'streame-theme'),
        'type' => 'array'
    ));
    
    if ($paginate_links) {
        echo '<nav class="pagination">';
        echo '<ul class="page-numbers">';
        foreach ($paginate_links as $link) {
            echo '<li>' . $link . '</li>';
        }
        echo '</ul>';
        echo '</nav>';
    }
}

/**
 * Custom search form
 */
function streame_search_form() {
    $form = '<form role="search" method="get" class="search-form" action="' . home_url('/') . '">
        <input type="search" class="search-field" placeholder="' . esc_attr__('Search movies, TV shows...', 'streame-theme') . '" value="' . get_search_query() . '" name="s" />
        <button type="submit" class="search-submit">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </form>';
    
    return $form;
}

/**
 * Include template parts
 */
function streame_get_template_part($slug, $name = null, $args = array()) {
    if ($args && is_array($args)) {
        extract($args);
    }
    
    $template = '';
    
    if ($name) {
        $template = locate_template(array("{$slug}-{$name}.php", "{$slug}.php"));
    } else {
        $template = locate_template("{$slug}.php");
    }
    
    if ($template) {
        include $template;
    }
}

/**
 * Check if Streame plugin is active
 */
function streame_plugin_active() {
    return class_exists('StreamePlugin');
}

/**
 * Admin notice if plugin not active
 */
function streame_theme_admin_notice() {
    if (!streame_plugin_active() && current_user_can('manage_options')) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p>' . sprintf(
            __('The Streame theme works best with the Streame plugin. <a href="%s">Install and activate the plugin</a> for full functionality.', 'streame-theme'),
            admin_url('plugins.php')
        ) . '</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'streame_theme_admin_notice');
