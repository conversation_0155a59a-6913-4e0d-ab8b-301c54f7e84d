    <footer id="colophon" class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><?php _e('About', 'streame-theme'); ?></h3>
                    <p><?php bloginfo('description'); ?></p>
                    <?php if (streame_plugin_active()): ?>
                        <p><?php _e('Stream your favorite movies and TV shows in high quality.', 'streame-theme'); ?></p>
                    <?php endif; ?>
                </div>
                
                <div class="footer-section">
                    <h3><?php _e('Quick Links', 'streame-theme'); ?></h3>
                    <ul>
                        <li><a href="<?php echo esc_url(home_url('/')); ?>"><?php _e('Home', 'streame-theme'); ?></a></li>
                        <?php if (streame_plugin_active()): ?>
                            <li><a href="<?php echo esc_url(get_post_type_archive_link('streame_movie')); ?>"><?php _e('Movies', 'streame-theme'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_post_type_archive_link('streame_tv_show')); ?>"><?php _e('TV Shows', 'streame-theme'); ?></a></li>
                        <?php endif; ?>
                        <?php if (get_option('show_on_front') == 'page'): ?>
                            <li><a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>"><?php _e('Blog', 'streame-theme'); ?></a></li>
                        <?php endif; ?>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3><?php _e('Genres', 'streame-theme'); ?></h3>
                    <?php if (streame_plugin_active()): ?>
                        <ul>
                            <?php
                            $genres = get_terms(array(
                                'taxonomy' => 'streame_genre',
                                'number' => 6,
                                'orderby' => 'count',
                                'order' => 'DESC'
                            ));
                            
                            if ($genres && !is_wp_error($genres)):
                                foreach ($genres as $genre):
                            ?>
                                <li><a href="<?php echo get_term_link($genre); ?>"><?php echo esc_html($genre->name); ?></a></li>
                            <?php
                                endforeach;
                            endif;
                            ?>
                        </ul>
                    <?php else: ?>
                        <ul>
                            <li><a href="#"><?php _e('Action', 'streame-theme'); ?></a></li>
                            <li><a href="#"><?php _e('Comedy', 'streame-theme'); ?></a></li>
                            <li><a href="#"><?php _e('Drama', 'streame-theme'); ?></a></li>
                            <li><a href="#"><?php _e('Horror', 'streame-theme'); ?></a></li>
                            <li><a href="#"><?php _e('Sci-Fi', 'streame-theme'); ?></a></li>
                            <li><a href="#"><?php _e('Thriller', 'streame-theme'); ?></a></li>
                        </ul>
                    <?php endif; ?>
                </div>
                
                <div class="footer-section">
                    <h3><?php _e('Connect', 'streame-theme'); ?></h3>
                    <ul>
                        <li><a href="#" target="_blank" rel="noopener"><?php _e('Facebook', 'streame-theme'); ?></a></li>
                        <li><a href="#" target="_blank" rel="noopener"><?php _e('Twitter', 'streame-theme'); ?></a></li>
                        <li><a href="#" target="_blank" rel="noopener"><?php _e('Instagram', 'streame-theme'); ?></a></li>
                        <li><a href="#" target="_blank" rel="noopener"><?php _e('YouTube', 'streame-theme'); ?></a></li>
                    </ul>
                    
                    <?php if (is_active_sidebar('footer-1')): ?>
                        <?php dynamic_sidebar('footer-1'); ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('All rights reserved.', 'streame-theme'); ?></p>
                
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'footer',
                    'menu_class' => 'footer-menu',
                    'container' => 'nav',
                    'container_class' => 'footer-navigation',
                    'depth' => 1,
                    'fallback_cb' => false
                ));
                ?>
                
                <p class="powered-by">
                    <?php printf(__('Powered by %s', 'streame-theme'), '<a href="https://wordpress.org/" target="_blank" rel="noopener">WordPress</a>'); ?>
                    <?php if (streame_plugin_active()): ?>
                        <?php _e('& Streame Plugin', 'streame-theme'); ?>
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </footer>
</div><!-- #page -->

<?php wp_footer(); ?>

<!-- Back to Top Button -->
<button id="back-to-top" class="back-to-top" aria-label="<?php _e('Back to top', 'streame-theme'); ?>">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
</button>

<script>
// Back to top functionality
jQuery(document).ready(function($) {
    var $backToTop = $('#back-to-top');
    
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $backToTop.fadeIn();
        } else {
            $backToTop.fadeOut();
        }
    });
    
    $backToTop.click(function() {
        $('html, body').animate({scrollTop: 0}, 600);
        return false;
    });
    
    // Mobile menu toggle
    $('.mobile-menu-toggle').click(function() {
        var $nav = $('.main-navigation');
        var expanded = $(this).attr('aria-expanded') === 'true';
        
        $(this).attr('aria-expanded', !expanded);
        $nav.toggleClass('active');
    });
    
    // Close mobile menu when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.main-navigation, .mobile-menu-toggle').length) {
            $('.main-navigation').removeClass('active');
            $('.mobile-menu-toggle').attr('aria-expanded', 'false');
        }
    });
    
    // Smooth scrolling for anchor links
    $('a[href*="#"]:not([href="#"])').click(function() {
        if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
            var target = $(this.hash);
            target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 1000);
                return false;
            }
        }
    });
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Search form enhancements
    $('.search-form input[type="search"]').on('focus', function() {
        $(this).closest('.search-form').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.search-form').removeClass('focused');
    });
    
    // Content item hover effects
    $('.content-item').hover(
        function() {
            $(this).addClass('hovered');
        },
        function() {
            $(this).removeClass('hovered');
        }
    );
    
    // Keyboard navigation for content items
    $('.content-item').on('keydown', function(e) {
        if (e.which === 13 || e.which === 32) { // Enter or Space
            e.preventDefault();
            var $link = $(this).find('a').first();
            if ($link.length) {
                window.location.href = $link.attr('href');
            }
        }
    });
    
    // Add loading states
    $('form').on('submit', function() {
        var $submit = $(this).find('button[type="submit"], input[type="submit"]');
        $submit.prop('disabled', true).addClass('loading');
        
        setTimeout(function() {
            $submit.prop('disabled', false).removeClass('loading');
        }, 3000);
    });
    
    // Handle video thumbnails
    $('.content-item-image').on('click', function(e) {
        var $playBtn = $(this).find('.play-button');
        if ($playBtn.length && e.target !== $playBtn[0]) {
            $playBtn.click();
        }
    });
    
    // Accessibility improvements
    $('.content-item').attr('tabindex', '0').attr('role', 'button');
    
    // Performance: Debounce scroll events
    let scrollTimeout;
    $(window).on('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(function() {
            // Scroll-based animations can go here
        }, 10);
    });
});
</script>

</body>
</html>
