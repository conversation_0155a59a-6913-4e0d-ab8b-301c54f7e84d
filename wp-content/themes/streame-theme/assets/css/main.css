/* Additional Streame Theme Styles */

/* Archive Filters */
.archive-filters {
    background: #1e293b;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    color: #cbd5e1;
    font-size: 0.875rem;
}

.filter-group select {
    background: #334155;
    border: 1px solid #475569;
    color: #fff;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
}

.filter-group select:focus {
    outline: none;
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.view-toggle {
    flex-direction: row;
    min-width: auto;
}

.view-btn {
    background: #334155;
    border: 1px solid #475569;
    color: #94a3b8;
    padding: 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.view-btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.view-btn.active {
    background: #1e3a8a;
    border-color: #1e3a8a;
    color: #fff;
}

.archive-search {
    display: flex;
    justify-content: center;
}

.archive-search .search-form {
    max-width: 400px;
    width: 100%;
}

/* Content List View */
.content-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.content-list .content-item {
    display: flex;
    background: #1e293b;
    border-radius: 12px;
    overflow: hidden;
    padding: 0;
}

.content-list .content-item-image {
    width: 200px;
    flex-shrink: 0;
    aspect-ratio: 2/3;
}

.content-list .content-item-info {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.content-list .content-item-title {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

.content-list .content-item-meta {
    margin-bottom: 1rem;
}

.content-list .content-item-description {
    flex: 1;
    margin-bottom: 1rem;
}

.content-list .content-item-actions {
    display: flex;
    gap: 1rem;
}

/* Load More Section */
.load-more-section {
    text-align: center;
    margin: 3rem 0;
}

.load-more-section .btn {
    min-width: 200px;
}

/* No Content Message */
.no-content-message {
    text-align: center;
    padding: 4rem 2rem;
    background: #1e293b;
    border-radius: 12px;
}

.no-content-message h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #fff;
}

.no-content-message p {
    font-size: 1.125rem;
    color: #94a3b8;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.no-content-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Popular Genres */
.popular-genres {
    margin-top: 4rem;
}

.popular-genres h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.genres-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.genre-item {
    background: #1e293b;
    border-radius: 12px;
    overflow: hidden;
    aspect-ratio: 16/9;
    background-size: cover;
    background-position: center;
    position: relative;
}

.genre-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(30, 58, 138, 0.8), rgba(15, 23, 42, 0.8));
    transition: opacity 0.3s ease;
}

.genre-item:hover::before {
    opacity: 0.9;
}

.genre-link {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-decoration: none;
    color: #fff;
    position: relative;
    z-index: 1;
    padding: 2rem;
    text-align: center;
}

.genre-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.genre-count {
    font-size: 0.875rem;
    color: #cbd5e1;
}

/* Episodes Grid */
.episodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.episode-item {
    background: #1e293b;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.episode-item:hover {
    transform: translateY(-5px);
}

.episode-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    background: #334155;
}

.episode-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #334155;
    color: #94a3b8;
    font-size: 1.5rem;
    font-weight: 700;
}

.episode-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.episode-item:hover .episode-overlay {
    opacity: 1;
}

.play-episode {
    background: #1e3a8a;
    color: #fff;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.play-episode:hover {
    background: #1e40af;
    transform: scale(1.1);
}

.episode-info {
    padding: 1rem;
}

.episode-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.episode-title .show-title {
    color: #1e3a8a;
    text-decoration: none;
}

.episode-title .show-title:hover {
    color: #1e40af;
}

.episode-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: #94a3b8;
}

.episode-number {
    background: #334155;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
}

.episode-actions {
    margin-top: 0.75rem;
}

/* Season Sections */
.season-section {
    margin-bottom: 3rem;
}

.season-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #334155;
}

.episode-count {
    font-size: 0.875rem;
    color: #94a3b8;
    font-weight: 400;
}

/* Movie/TV Show Details */
.movie-hero,
.tv-show-hero {
    background-size: cover;
    background-position: center;
    padding: 4rem 0;
    position: relative;
}

.movie-hero-content,
.tv-show-hero-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    align-items: start;
}

.movie-poster,
.tv-show-poster {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.movie-poster img,
.tv-show-poster img {
    width: 100%;
    height: auto;
    display: block;
}

.movie-info,
.tv-show-info {
    color: #fff;
}

.movie-title,
.tv-show-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.movie-meta,
.tv-show-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.movie-rating,
.tv-show-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(30, 58, 138, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
}

.movie-year,
.tv-show-year,
.movie-runtime,
.tv-show-seasons,
.tv-show-episodes {
    color: #cbd5e1;
    font-weight: 500;
}

.movie-genres,
.tv-show-genres {
    color: #94a3b8;
}

.movie-description,
.tv-show-description {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #e2e8f0;
}

.movie-actions,
.tv-show-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Details Grid */
.details-grid {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 3rem;
    margin-top: 3rem;
}

.details-main {
    background: #1e293b;
    border-radius: 12px;
    padding: 2rem;
}

.details-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-poster {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.sidebar-poster img {
    width: 100%;
    height: auto;
    display: block;
}

.quick-stats {
    background: #1e293b;
    border-radius: 12px;
    padding: 1.5rem;
}

.quick-stats h3 {
    margin-bottom: 1rem;
    color: #fff;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #334155;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #94a3b8;
    font-size: 0.875rem;
}

.stat-value {
    color: #fff;
    font-weight: 600;
}

/* Info Grid */
.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #334155;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: #cbd5e1;
    min-width: 120px;
    font-size: 0.875rem;
}

.info-item span {
    color: #94a3b8;
    font-size: 0.875rem;
}

/* Related Items */
.related-movies,
.related-shows {
    background: #1e293b;
    border-radius: 12px;
    padding: 1.5rem;
}

.related-movies h3,
.related-shows h3 {
    margin-bottom: 1rem;
    color: #fff;
}

.related-movies-list,
.related-shows-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-movie-item,
.related-show-item {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.related-movie-poster,
.related-show-poster {
    width: 60px;
    flex-shrink: 0;
    border-radius: 6px;
    overflow: hidden;
}

.related-movie-poster img,
.related-show-poster img {
    width: 100%;
    height: auto;
    display: block;
}

.related-movie-info,
.related-show-info {
    flex: 1;
}

.related-movie-info h4,
.related-show-info h4 {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.related-movie-info h4 a,
.related-show-info h4 a {
    color: #fff;
    text-decoration: none;
}

.related-movie-info h4 a:hover,
.related-show-info h4 a:hover {
    color: #1e3a8a;
}

.related-movie-meta,
.related-show-meta {
    display: flex;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #94a3b8;
}

/* Button Sizes */
.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-large {
    padding: 1rem 2.5rem;
    font-size: 1.125rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .details-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .details-sidebar {
        order: -1;
    }
    
    .movie-hero-content,
    .tv-show-hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .movie-poster,
    .tv-show-poster {
        max-width: 300px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .view-toggle {
        align-self: center;
    }
    
    .content-list .content-item {
        flex-direction: column;
    }
    
    .content-list .content-item-image {
        width: 100%;
        aspect-ratio: 16/9;
    }
    
    .episodes-grid {
        grid-template-columns: 1fr;
    }
    
    .movie-title,
    .tv-show-title {
        font-size: 2rem;
    }
    
    .movie-actions,
    .tv-show-actions {
        justify-content: center;
    }
    
    .genres-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .archive-filters {
        padding: 1.5rem;
    }
    
    .no-content-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .movie-hero,
    .tv-show-hero {
        padding: 2rem 0;
    }
    
    .movie-title,
    .tv-show-title {
        font-size: 1.75rem;
    }
    
    .movie-meta,
    .tv-show-meta {
        justify-content: center;
    }
}
