/* Streame Theme Main JavaScript */

jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize theme functionality
    initializeTheme();
    
    /**
     * Initialize all theme functionality
     */
    function initializeTheme() {
        initializeSearch();
        initializeFilters();
        initializeWatchlist();
        initializeSharing();
        initializeLazyLoading();
        initializeAccessibility();
        initializePerformance();
    }
    
    /**
     * Enhanced search functionality
     */
    function initializeSearch() {
        var $searchForm = $('.search-form');
        var $searchInput = $searchForm.find('input[type="search"]');
        var searchTimeout;
        
        // Live search suggestions
        $searchInput.on('input', function() {
            var query = $(this).val().trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 3) {
                searchTimeout = setTimeout(function() {
                    showSearchSuggestions(query);
                }, 300);
            } else {
                hideSearchSuggestions();
            }
        });
        
        // Handle search form submission
        $searchForm.on('submit', function(e) {
            var query = $searchInput.val().trim();
            if (query.length < 3) {
                e.preventDefault();
                showNotification('Please enter at least 3 characters', 'warning');
            }
        });
        
        // Close suggestions when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-form').length) {
                hideSearchSuggestions();
            }
        });
    }
    
    /**
     * Show search suggestions
     */
    function showSearchSuggestions(query) {
        if (!streame_theme.ajax_url) return;
        
        $.ajax({
            url: streame_theme.ajax_url,
            type: 'POST',
            data: {
                action: 'streame_search_suggestions',
                query: query,
                nonce: streame_theme.nonce
            },
            success: function(response) {
                if (response.success && response.data.suggestions) {
                    displaySearchSuggestions(response.data.suggestions);
                }
            }
        });
    }
    
    /**
     * Display search suggestions
     */
    function displaySearchSuggestions(suggestions) {
        var $searchForm = $('.search-form');
        var $existing = $searchForm.find('.search-suggestions');
        
        if ($existing.length) {
            $existing.remove();
        }
        
        if (suggestions.length === 0) return;
        
        var html = '<div class="search-suggestions">';
        suggestions.forEach(function(item) {
            var title = item.title || item.name;
            var type = item.media_type || 'movie';
            var url = item.url || '#';
            
            html += '<div class="suggestion-item" data-url="' + url + '">';
            html += '<span class="suggestion-title">' + title + '</span>';
            html += '<span class="suggestion-type">' + type + '</span>';
            html += '</div>';
        });
        html += '</div>';
        
        $searchForm.append(html);
        
        // Handle suggestion clicks
        $('.suggestion-item').on('click', function() {
            var url = $(this).data('url');
            if (url && url !== '#') {
                window.location.href = url;
            }
        });
    }
    
    /**
     * Hide search suggestions
     */
    function hideSearchSuggestions() {
        $('.search-suggestions').remove();
    }
    
    /**
     * Initialize filter functionality
     */
    function initializeFilters() {
        // Save filter state
        $('.archive-filters select').on('change', function() {
            var filterId = $(this).attr('id');
            var filterValue = $(this).val();
            
            localStorage.setItem('streame_filter_' + filterId, filterValue);
        });
        
        // Restore filter state
        $('.archive-filters select').each(function() {
            var filterId = $(this).attr('id');
            var savedValue = localStorage.getItem('streame_filter_' + filterId);
            
            if (savedValue) {
                $(this).val(savedValue);
            }
        });
        
        // Clear filters button
        if ($('.clear-filters').length === 0) {
            $('.archive-filters').append('<button class="btn btn-secondary clear-filters">Clear Filters</button>');
        }
        
        $('.clear-filters').on('click', function() {
            $('.archive-filters select').val('');
            $('.archive-filters select').first().trigger('change');
            
            // Clear localStorage
            $('.archive-filters select').each(function() {
                var filterId = $(this).attr('id');
                localStorage.removeItem('streame_filter_' + filterId);
            });
        });
    }
    
    /**
     * Initialize watchlist functionality
     */
    function initializeWatchlist() {
        $('.add-to-watchlist').on('click', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var movieId = $button.data('movie-id') || $button.data('show-id');
            var isAdded = $button.hasClass('added');
            
            if (!movieId) return;
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: streame_theme.ajax_url,
                type: 'POST',
                data: {
                    action: isAdded ? 'remove_from_watchlist' : 'add_to_watchlist',
                    item_id: movieId,
                    nonce: streame_theme.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $button.toggleClass('added');
                        
                        var newText = $button.hasClass('added') ? 
                            streame_theme.strings.added || 'Added' : 
                            streame_theme.strings.add_to_watchlist || 'Add to Watchlist';
                        
                        $button.text(newText);
                        
                        showNotification(response.data.message, 'success');
                    } else {
                        showNotification(response.data || 'Error updating watchlist', 'error');
                    }
                },
                error: function() {
                    showNotification('Network error', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        });
    }
    
    /**
     * Initialize sharing functionality
     */
    function initializeSharing() {
        $('.share-movie, .share-show').on('click', function(e) {
            e.preventDefault();
            
            var url = $(this).data('url');
            var title = $(this).data('title');
            
            if (navigator.share) {
                // Use native sharing if available
                navigator.share({
                    title: title,
                    url: url
                }).catch(function(error) {
                    console.log('Error sharing:', error);
                });
            } else {
                // Fallback to custom share modal
                showShareModal(url, title);
            }
        });
    }
    
    /**
     * Show custom share modal
     */
    function showShareModal(url, title) {
        var encodedUrl = encodeURIComponent(url);
        var encodedTitle = encodeURIComponent(title);
        
        var shareLinks = {
            facebook: 'https://www.facebook.com/sharer/sharer.php?u=' + encodedUrl,
            twitter: 'https://twitter.com/intent/tweet?url=' + encodedUrl + '&text=' + encodedTitle,
            linkedin: 'https://www.linkedin.com/sharing/share-offsite/?url=' + encodedUrl,
            whatsapp: 'https://wa.me/?text=' + encodedTitle + ' ' + encodedUrl
        };
        
        var modal = '<div class="share-modal-overlay">';
        modal += '<div class="share-modal">';
        modal += '<h3>Share this content</h3>';
        modal += '<div class="share-buttons">';
        
        Object.keys(shareLinks).forEach(function(platform) {
            modal += '<a href="' + shareLinks[platform] + '" target="_blank" class="share-btn share-' + platform + '">';
            modal += platform.charAt(0).toUpperCase() + platform.slice(1);
            modal += '</a>';
        });
        
        modal += '</div>';
        modal += '<button class="close-share-modal">Close</button>';
        modal += '</div>';
        modal += '</div>';
        
        $('body').append(modal);
        
        // Close modal handlers
        $('.close-share-modal, .share-modal-overlay').on('click', function(e) {
            if (e.target === this) {
                $('.share-modal-overlay').remove();
            }
        });
    }
    
    /**
     * Initialize lazy loading for images
     */
    function initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px'
            });
            
            // Observe all lazy images
            document.querySelectorAll('img[data-src]').forEach(function(img) {
                img.classList.add('lazy');
                imageObserver.observe(img);
            });
        }
    }
    
    /**
     * Initialize accessibility features
     */
    function initializeAccessibility() {
        // Skip to content link
        if ($('.skip-link').length === 0) {
            $('body').prepend('<a class="skip-link screen-reader-text" href="#primary">Skip to content</a>');
        }
        
        // Focus management for modals
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                $('.share-modal-overlay').remove();
                $('.search-suggestions').remove();
            }
        });
        
        // Announce dynamic content changes
        if ($('#live-region').length === 0) {
            $('body').append('<div id="live-region" aria-live="polite" aria-atomic="true" class="screen-reader-text"></div>');
        }
        
        // High contrast mode detection
        if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
            $('body').addClass('high-contrast');
        }
        
        // Reduced motion detection
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            $('body').addClass('reduced-motion');
        }
    }
    
    /**
     * Initialize performance optimizations
     */
    function initializePerformance() {
        // Debounce scroll events
        var scrollTimeout;
        $(window).on('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                handleScroll();
            }, 16); // ~60fps
        });
        
        // Preload critical resources
        preloadCriticalResources();
        
        // Service worker registration (if available)
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(function(error) {
                console.log('Service Worker registration failed:', error);
            });
        }
    }
    
    /**
     * Handle scroll events
     */
    function handleScroll() {
        var scrollTop = $(window).scrollTop();
        
        // Show/hide back to top button
        if (scrollTop > 300) {
            $('#back-to-top').fadeIn();
        } else {
            $('#back-to-top').fadeOut();
        }
        
        // Parallax effect for hero sections (if not reduced motion)
        if (!$('body').hasClass('reduced-motion')) {
            $('.movie-hero, .tv-show-hero').each(function() {
                var $hero = $(this);
                var heroTop = $hero.offset().top;
                var heroHeight = $hero.outerHeight();
                var windowHeight = $(window).height();
                
                if (scrollTop + windowHeight > heroTop && scrollTop < heroTop + heroHeight) {
                    var parallaxSpeed = 0.5;
                    var yPos = -(scrollTop - heroTop) * parallaxSpeed;
                    $hero.css('background-position', 'center ' + yPos + 'px');
                }
            });
        }
    }
    
    /**
     * Preload critical resources
     */
    function preloadCriticalResources() {
        // Preload fonts
        var fontUrls = [
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap'
        ];
        
        fontUrls.forEach(function(url) {
            var link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = url;
            document.head.appendChild(link);
        });
        
        // Preload critical images
        $('.content-item-image img').slice(0, 6).each(function() {
            var img = new Image();
            img.src = $(this).attr('src');
        });
    }
    
    /**
     * Show notification
     */
    function showNotification(message, type) {
        type = type || 'info';
        
        var notification = '<div class="notification notification-' + type + '">';
        notification += '<span class="notification-message">' + message + '</span>';
        notification += '<button class="notification-close">&times;</button>';
        notification += '</div>';
        
        if ($('.notifications-container').length === 0) {
            $('body').append('<div class="notifications-container"></div>');
        }
        
        var $notification = $(notification);
        $('.notifications-container').append($notification);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual close
        $notification.find('.notification-close').on('click', function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        });
        
        // Announce to screen readers
        $('#live-region').text(message);
    }
    
    /**
     * Utility function to get URL parameters
     */
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }
    
    /**
     * Utility function to update URL parameters
     */
    function updateUrlParameter(url, param, paramVal) {
        var newAdditionalURL = "";
        var tempArray = url.split("?");
        var baseURL = tempArray[0];
        var additionalURL = tempArray[1];
        var temp = "";
        
        if (additionalURL) {
            tempArray = additionalURL.split("&");
            for (var i = 0; i < tempArray.length; i++) {
                if (tempArray[i].split('=')[0] != param) {
                    newAdditionalURL += temp + tempArray[i];
                    temp = "&";
                }
            }
        }
        
        var rows_txt = temp + "" + param + "=" + paramVal;
        return baseURL + "?" + newAdditionalURL + rows_txt;
    }
    
    // Initialize tooltips
    $('[data-tooltip]').hover(
        function() {
            var tooltip = $(this).data('tooltip');
            $(this).append('<div class="tooltip-popup">' + tooltip + '</div>');
        },
        function() {
            $(this).find('.tooltip-popup').remove();
        }
    );
    
    // Initialize progress indicators
    $('.progress-bar').each(function() {
        var $bar = $(this);
        var progress = $bar.data('progress') || 0;
        
        $bar.find('.progress-fill').animate({
            width: progress + '%'
        }, 1000);
    });
    
    // Handle form submissions with loading states
    $('form').on('submit', function() {
        var $form = $(this);
        var $submit = $form.find('button[type="submit"], input[type="submit"]');
        
        $submit.prop('disabled', true).addClass('loading');
        
        // Re-enable after timeout (fallback)
        setTimeout(function() {
            $submit.prop('disabled', false).removeClass('loading');
        }, 10000);
    });
    
    // Initialize theme on window load
    $(window).on('load', function() {
        // Remove loading states
        $('.loading').removeClass('loading');
        
        // Trigger scroll handler once
        handleScroll();
        
        // Initialize any remaining functionality
        $('body').addClass('theme-loaded');
    });
});
