<?php
/**
 * Template for displaying single movie
 */

get_header(); ?>

<main id="primary" class="site-main">
    <?php while (have_posts()): the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class('movie-single'); ?>>
            
            <?php
            // Get movie metadata
            $tmdb_id = get_post_meta(get_the_ID(), '_streame_tmdb_id', true);
            $poster_url = get_post_meta(get_the_ID(), '_streame_poster_url', true);
            $backdrop_url = get_post_meta(get_the_ID(), '_streame_backdrop_url', true);
            $rating = streame_get_rating();
            $year = streame_get_year();
            $release_date = get_post_meta(get_the_ID(), '_streame_release_date', true);
            $runtime = get_post_meta(get_the_ID(), '_streame_runtime', true);
            $genres = streame_get_genres();
            ?>
            
            <!-- Hero Section with Backdrop -->
            <section class="movie-hero" <?php if ($backdrop_url): ?>style="background-image: linear-gradient(rgba(15, 23, 42, 0.7), rgba(15, 23, 42, 0.9)), url('<?php echo esc_url($backdrop_url); ?>');"<?php endif; ?>>
                <div class="container">
                    <div class="movie-hero-content">
                        <?php if ($poster_url): ?>
                            <div class="movie-poster">
                                <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" />
                            </div>
                        <?php endif; ?>
                        
                        <div class="movie-info">
                            <h1 class="movie-title"><?php the_title(); ?></h1>
                            
                            <div class="movie-meta">
                                <?php if ($rating): ?>
                                    <span class="movie-rating">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/>
                                        </svg>
                                        <?php echo $rating; ?>/10
                                    </span>
                                <?php endif; ?>
                                
                                <?php if ($year): ?>
                                    <span class="movie-year"><?php echo $year; ?></span>
                                <?php endif; ?>
                                
                                <?php if ($runtime): ?>
                                    <span class="movie-runtime">
                                        <?php printf(__('%d min', 'streame-theme'), $runtime); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <?php if (!empty($genres)): ?>
                                    <span class="movie-genres">
                                        <?php streame_display_genres(); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="movie-description">
                                <?php the_content(); ?>
                            </div>
                            
                            <div class="movie-actions">
                                <?php streame_display_play_button(get_the_ID(), __('Watch Now', 'streame-theme')); ?>
                                
                                <button class="btn btn-secondary add-to-watchlist" data-movie-id="<?php echo get_the_ID(); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M19 14C19 18.4183 12 23 12 23C12 23 5 18.4183 5 14C5 11.2386 7.23858 9 10 9C10.7403 9 11.4215 9.19716 12 9.53799C12.5785 9.19716 13.2597 9 14 9C16.7614 9 19 11.2386 19 14Z"/>
                                    </svg>
                                    <?php _e('Add to Watchlist', 'streame-theme'); ?>
                                </button>
                                
                                <button class="btn btn-secondary share-movie" data-url="<?php echo get_permalink(); ?>" data-title="<?php echo esc_attr(get_the_title()); ?>">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M4 12V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V12"/>
                                        <path d="M16 6L12 2L8 6"/>
                                        <path d="M12 2V15"/>
                                    </svg>
                                    <?php _e('Share', 'streame-theme'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Movie Details -->
            <section class="movie-details">
                <div class="container">
                    <div class="details-grid">
                        <div class="details-main">
                            
                            <!-- Additional Info -->
                            <div class="movie-additional-info">
                                <h2><?php _e('Details', 'streame-theme'); ?></h2>
                                
                                <div class="info-grid">
                                    <?php if ($release_date): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Release Date:', 'streame-theme'); ?></strong>
                                            <span><?php echo date('F j, Y', strtotime($release_date)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($runtime): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Runtime:', 'streame-theme'); ?></strong>
                                            <span><?php printf(__('%d minutes', 'streame-theme'), $runtime); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($genres)): ?>
                                        <div class="info-item">
                                            <strong><?php _e('Genres:', 'streame-theme'); ?></strong>
                                            <span><?php streame_display_genres(); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($tmdb_id): ?>
                                        <div class="info-item">
                                            <strong><?php _e('TMDB ID:', 'streame-theme'); ?></strong>
                                            <span><?php echo $tmdb_id; ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Cast & Crew (if available from TMDB) -->
                            <?php if (class_exists('Streame_TMDB_API') && $tmdb_id): ?>
                                <div class="movie-cast-crew">
                                    <h2><?php _e('Cast & Crew', 'streame-theme'); ?></h2>
                                    <div id="cast-crew-content" data-tmdb-id="<?php echo $tmdb_id; ?>" data-media-type="movie">
                                        <div class="loading"><?php _e('Loading cast information...', 'streame-theme'); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                        </div>
                        
                        <div class="details-sidebar">
                            <!-- Movie Poster -->
                            <?php if ($poster_url): ?>
                                <div class="sidebar-poster">
                                    <img src="<?php echo esc_url($poster_url); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" />
                                </div>
                            <?php endif; ?>
                            
                            <!-- Quick Stats -->
                            <div class="quick-stats">
                                <h3><?php _e('Quick Stats', 'streame-theme'); ?></h3>
                                
                                <?php if ($rating): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('Rating', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php echo $rating; ?>/10</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($year): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('Year', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php echo $year; ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($runtime): ?>
                                    <div class="stat-item">
                                        <span class="stat-label"><?php _e('Duration', 'streame-theme'); ?></span>
                                        <span class="stat-value"><?php printf(__('%dh %dm', 'streame-theme'), floor($runtime / 60), $runtime % 60); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Related Movies by Genre -->
                            <?php if (!empty($genres)): ?>
                                <div class="related-movies">
                                    <h3><?php _e('More Like This', 'streame-theme'); ?></h3>
                                    
                                    <?php
                                    $related_movies = get_posts(array(
                                        'post_type' => 'streame_movie',
                                        'numberposts' => 4,
                                        'post__not_in' => array(get_the_ID()),
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => 'streame_genre',
                                                'field' => 'term_id',
                                                'terms' => wp_list_pluck($genres, 'term_id')
                                            )
                                        )
                                    ));
                                    
                                    if ($related_movies):
                                    ?>
                                        <div class="related-movies-list">
                                            <?php foreach ($related_movies as $related_movie): ?>
                                                <div class="related-movie-item">
                                                    <?php
                                                    $related_poster = get_post_meta($related_movie->ID, '_streame_poster_url', true);
                                                    if ($related_poster):
                                                    ?>
                                                        <div class="related-movie-poster">
                                                            <a href="<?php echo get_permalink($related_movie->ID); ?>">
                                                                <img src="<?php echo esc_url($related_poster); ?>" alt="<?php echo esc_attr($related_movie->post_title); ?>" />
                                                            </a>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                    <div class="related-movie-info">
                                                        <h4>
                                                            <a href="<?php echo get_permalink($related_movie->ID); ?>">
                                                                <?php echo esc_html($related_movie->post_title); ?>
                                                            </a>
                                                        </h4>
                                                        
                                                        <?php
                                                        $related_rating = streame_get_rating($related_movie->ID);
                                                        $related_year = streame_get_year($related_movie->ID);
                                                        ?>
                                                        
                                                        <div class="related-movie-meta">
                                                            <?php if ($related_rating): ?>
                                                                <span class="rating"><?php echo $related_rating; ?></span>
                                                            <?php endif; ?>
                                                            
                                                            <?php if ($related_year): ?>
                                                                <span class="year"><?php echo $related_year; ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Recommendations Section -->
            <?php if (class_exists('Streame_TMDB_API') && $tmdb_id): ?>
                <section class="movie-recommendations">
                    <div class="container">
                        <h2><?php _e('Maybe You\'ll Like', 'streame-theme'); ?></h2>
                        <div id="recommendations-content" data-tmdb-id="<?php echo $tmdb_id; ?>" data-media-type="movie">
                            <div class="loading"><?php _e('Loading recommendations...', 'streame-theme'); ?></div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>
            
            <!-- Navigation -->
            <section class="movie-navigation">
                <div class="container">
                    <?php streame_theme_post_navigation(); ?>
                </div>
            </section>
            
        </article>
    <?php endwhile; ?>
</main>

<?php get_footer(); ?>
