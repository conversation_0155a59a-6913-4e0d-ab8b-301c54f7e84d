<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#primary"><?php _e('Skip to content', 'streame-theme'); ?></a>

    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-branding">
                    <?php
                    if (has_custom_logo()) {
                        the_custom_logo();
                    } else {
                        ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="site-logo" rel="home">
                            <?php bloginfo('name'); ?>
                        </a>
                        <?php
                    }
                    ?>
                </div>

                <nav id="site-navigation" class="main-navigation">
                    <div class="header-search">
                        <?php echo streame_search_form(); ?>
                    </div>
                    
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id' => 'primary-menu',
                        'menu_class' => 'nav-menu',
                        'container' => false,
                        'fallback_cb' => 'streame_fallback_menu'
                    ));
                    ?>
                    
                    <button class="mobile-menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                        <span class="screen-reader-text"><?php _e('Menu', 'streame-theme'); ?></span>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 12H21M3 6H21M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </nav>
            </div>
        </div>
    </header>

<?php
/**
 * Fallback menu when no menu is assigned
 */
function streame_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . __('Home', 'streame-theme') . '</a></li>';
    
    if (streame_plugin_active()) {
        echo '<li><a href="' . esc_url(get_post_type_archive_link('streame_movie')) . '">' . __('Movies', 'streame-theme') . '</a></li>';
        echo '<li><a href="' . esc_url(get_post_type_archive_link('streame_tv_show')) . '">' . __('TV Shows', 'streame-theme') . '</a></li>';
        echo '<li><a href="' . esc_url(get_term_link(get_terms('streame_genre', array('number' => 1))[0] ?? '')) . '">' . __('Genres', 'streame-theme') . '</a></li>';
    }
    
    if (get_option('show_on_front') == 'page') {
        echo '<li><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . __('Blog', 'streame-theme') . '</a></li>';
    }
    
    echo '</ul>';
}
?>
