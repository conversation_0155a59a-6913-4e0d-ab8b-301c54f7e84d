# Streame - WordPress Movie & TV Streaming Platform

A complete WordPress solution for creating movie and TV show streaming websites. This project includes both a powerful plugin and a beautiful dark theme designed specifically for streaming content.

## 🎬 Features

### Plugin Features
- **Custom Post Types**: Movies, TV Shows, and Episodes
- **TMDB Integration**: Automatic metadata fetching from The Movie Database
- **Multiple Embed Sources**: Support for up to 10 streaming sources
- **Advanced Search**: Filter by genre, year, rating, and more
- **Responsive Player**: Full-screen video player with source switching
- **Admin Dashboard**: Easy content management and settings
- **Import/Export**: Bulk import from TMDB and settings backup
- **Caching System**: Optimized performance with smart caching

### Theme Features
- **Dark Design**: Modern, cinema-inspired dark theme
- **Responsive Layout**: Perfect on all devices
- **Grid & List Views**: Multiple content display options
- **Advanced Filtering**: Genre, year, and rating filters
- **SEO Optimized**: Clean, semantic HTML structure
- **Accessibility**: WCAG compliant with keyboard navigation
- **Performance**: Lazy loading and optimized assets

## 📋 Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher
- TMDB API key (free registration required)

## 🚀 Installation

### Plugin Installation

1. Upload the `streame-plugin` folder to `/wp-content/plugins/`
2. Activate the plugin through the WordPress admin
3. Go to **Streame > Settings** and enter your TMDB API key
4. Configure your embed sources in **Streame > Embed Sources**

### Theme Installation

1. Upload the `streame-theme` folder to `/wp-content/themes/`
2. Activate the theme in **Appearance > Themes**
3. Customize the theme in **Appearance > Customize**

## ⚙️ Configuration

### Getting TMDB API Key

1. Visit [TMDB](https://www.themoviedb.org/)
2. Create a free account
3. Go to Settings > API
4. Request an API key
5. Enter the key in **Streame > Settings**

### Setting Up Embed Sources

The plugin comes with default embed sources, but you can customize them:

1. Go to **Streame > Embed Sources**
2. Configure URL patterns using placeholders:
   - `{TMDB_ID}` - The Movie Database ID
   - `{SEASON}` - Season number (TV shows)
   - `{EPISODE}` - Episode number (TV shows)

Example patterns:
- Movie: `https://example.com/movie/{TMDB_ID}`
- TV Show: `https://example.com/tv/{TMDB_ID}/s{SEASON}e{EPISODE}`

### Content Management

#### Adding Movies
1. Go to **Movies > Add New**
2. Enter the TMDB ID to auto-fetch metadata
3. Customize content as needed
4. Publish the movie

#### Adding TV Shows
1. Go to **TV Shows > Add New**
2. Enter the TMDB ID to auto-fetch metadata
3. Add individual episodes in **Episodes**
4. Link episodes to the TV show

#### Bulk Import
1. Go to **Streame > Import/Export**
2. Select content types to import
3. Choose the number of items
4. Click "Start Bulk Import"

## 🎨 Theme Customization

### Customizer Options
- Site logo and colors
- Homepage layout
- Featured content settings
- Footer customization

### Template Hierarchy
- `index.php` - Homepage with featured content
- `single-streame_movie.php` - Individual movie pages
- `single-streame_tv_show.php` - TV show pages with episodes
- `archive-streame_movie.php` - Movies archive with filters
- `archive-streame_tv_show.php` - TV shows archive

### Custom Functions
The theme includes helper functions:
- `streame_get_rating()` - Get content rating
- `streame_get_year()` - Get release/air year
- `streame_get_play_url()` - Generate streaming URL
- `streame_display_genres()` - Show content genres

## 🔧 Developer Guide

### Plugin Hooks

#### Actions
```php
// Before content import
do_action('streame_before_import', $tmdb_id, $media_type);

// After content import
do_action('streame_after_import', $post_id, $tmdb_data);

// Before player load
do_action('streame_before_player', $content_data);
```

#### Filters
```php
// Modify TMDB data before saving
$data = apply_filters('streame_tmdb_data', $data, $tmdb_id, $media_type);

// Customize embed sources
$sources = apply_filters('streame_embed_sources', $sources);

// Modify player template
$template = apply_filters('streame_player_template', $template, $content_data);
```

### Theme Hooks

#### Actions
```php
// Custom header content
do_action('streame_header_content');

// Before content grid
do_action('streame_before_content_grid');

// After content grid
do_action('streame_after_content_grid');
```

#### Filters
```php
// Modify content grid query
$query = apply_filters('streame_content_query', $query, $post_type);

// Customize archive filters
$filters = apply_filters('streame_archive_filters', $filters);
```

### Custom Post Types

#### Movie Meta Fields
- `_streame_tmdb_id` - TMDB ID
- `_streame_rating` - Rating (0-10)
- `_streame_release_date` - Release date
- `_streame_runtime` - Runtime in minutes
- `_streame_poster_url` - Poster image URL
- `_streame_backdrop_url` - Backdrop image URL

#### TV Show Meta Fields
- `_streame_tmdb_id` - TMDB ID
- `_streame_rating` - Rating (0-10)
- `_streame_first_air_date` - First air date
- `_streame_last_air_date` - Last air date
- `_streame_seasons` - Number of seasons
- `_streame_episodes` - Total episodes
- `_streame_poster_url` - Poster image URL
- `_streame_backdrop_url` - Backdrop image URL

#### Episode Meta Fields
- `_streame_tv_show_id` - Parent TV show ID
- `_streame_tmdb_id` - TMDB ID
- `_streame_season_number` - Season number
- `_streame_episode_number` - Episode number
- `_streame_air_date` - Air date
- `_streame_rating` - Rating (0-10)
- `_streame_runtime` - Runtime in minutes
- `_streame_still_path` - Episode still image URL

## 🎯 Usage Examples

### Displaying Movies in Templates
```php
$movies = get_posts(array(
    'post_type' => 'streame_movie',
    'numberposts' => 10,
    'meta_key' => '_streame_rating',
    'orderby' => 'meta_value_num',
    'order' => 'DESC'
));

foreach ($movies as $movie) {
    $rating = streame_get_rating($movie->ID);
    $year = streame_get_year($movie->ID);
    $play_url = streame_get_play_url($movie->ID);
    
    echo '<div class="movie-item">';
    echo '<h3>' . $movie->post_title . '</h3>';
    echo '<p>Rating: ' . $rating . '/10 (' . $year . ')</p>';
    if ($play_url) {
        echo '<a href="' . $play_url . '">Watch Now</a>';
    }
    echo '</div>';
}
```

### Custom Query with Filters
```php
$args = array(
    'post_type' => 'streame_movie',
    'posts_per_page' => 12,
    'tax_query' => array(
        array(
            'taxonomy' => 'streame_genre',
            'field' => 'slug',
            'terms' => 'action'
        )
    ),
    'meta_query' => array(
        array(
            'key' => '_streame_rating',
            'value' => 7.0,
            'compare' => '>='
        )
    )
);

$movies = new WP_Query($args);
```

## 🔒 Security

- All user inputs are sanitized and validated
- Nonce verification for AJAX requests
- Capability checks for admin functions
- SQL injection prevention with prepared statements
- XSS protection with proper escaping

## 🚀 Performance

- Database query optimization
- TMDB API response caching
- Lazy loading for images
- Minified CSS and JavaScript
- CDN-ready asset structure

## 📱 Browser Support

- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the GPL v2 or later - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Search existing issues
- Create a new issue with detailed information

## 🎉 Credits

- **TMDB**: Movie and TV show data
- **WordPress**: Content management system
- **Inter Font**: Typography
- **Heroicons**: Icon set

## 📈 Changelog

### Version 1.0.0
- Initial release
- Complete plugin functionality
- Dark theme with responsive design
- TMDB integration
- Multiple embed sources support
- Admin dashboard
- Import/export features

---

**Made with ❤️ for the WordPress community**
